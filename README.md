# RC DATA BIG5 文字提取和修改工具

这个工具可以从exe文件中提取RC DATA资源中的BIG5编码文字，导出到JSON文件进行编辑，然后将修改写回到新的exe文件中。

## 功能特性

- 提取exe文件中RC DATA资源的多种编码文字
- 支持BIG5、GBK、UTF-8、UTF-16编码自动识别
- 提取ASCII字符串和数值数据
- 发现并处理所有RCDATA资源（不仅仅是第一个）
- 将文字数据导出到JSON格式便于编辑
- 支持将修改后的文字写回exe文件
- 保持原始文件结构和其他数据不变
- 提供详细的数据分析和预览功能

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1: 使用交互式示例程序

```bash
python example.py
```

这会启动一个交互式菜单，指导你完成整个流程。

### 方法2: 直接使用命令行

#### 提取RC DATA到JSON

```bash
python rc_data_editor.py extract <exe文件路径> [输出json路径]
```

例子:
```bash
python rc_data_editor.py extract myapp.exe
# 会生成 myapp.exe_rc_data.json
```

#### 修改JSON文件

使用任何文本编辑器打开生成的JSON文件，找到`"text"`字段进行修改：

```json
{
  "RCDATA_1001_1033": {
    "resource_id": 1001,
    "language_id": 1033,
    "size": 256,
    "texts": [
      {
        "offset": 0,
        "length": 12,
        "text": "原始文字",     // 修改这里
        "encoding": "big5"
      }
    ]
  }
}
```

#### 将修改写回exe文件

```bash
python rc_data_editor.py writeback <原exe文件> <修改后json文件> [输出exe路径]
```

例子:
```bash
python rc_data_editor.py writeback myapp.exe myapp.exe_rc_data.json myapp_modified.exe
```

## JSON文件结构说明

```json
{
  "RCDATA_资源ID_语言ID": {
    "resource_id": 资源ID,
    "language_id": 语言ID, 
    "size": 资源大小,
    "rva": 相对虚拟地址,
    "raw_data_offset": 文件偏移,
    "texts": [
      {
        "offset": 文字在资源中的偏移,
        "length": 原始字节长度,
        "original_bytes": 原始字节的十六进制表示,
        "text": "实际文字内容",  // 这是你要修改的部分
        "encoding": "编码方式"
      }
    ]
  }
}
```

## 注意事项

1. **备份原文件**: 在修改前请务必备份原始exe文件
2. **文字长度限制**: 修改后的文字编码长度不能超过原始长度
3. **编码一致性**: 保持与原始文字相同的编码方式(BIG5/GBK)
4. **测试修改**: 修改后请测试exe文件是否正常运行

## 文件说明

- `rc_data_editor.py`: 主要的编辑器类和命令行接口
- `example.py`: 交互式使用示例
- `requirements.txt`: Python依赖包列表
- `README.md`: 本说明文件

## 技术原理

1. 使用pefile库解析PE格式的exe文件
2. 定位RT_RCDATA类型的资源（所有资源，不仅仅是第一个）
3. 在二进制数据中识别多种编码的文字字符串：
   - BIG5和GBK中文编码
   - UTF-8和UTF-16 Unicode编码
   - ASCII字符串
   - 数值数据（32位整数）
4. 将文字信息结构化导出到JSON，包含原始数据预览
5. 根据修改后的JSON重建二进制数据
6. 将新数据写回到exe文件的对应位置

## 改进说明

### v2.0 主要改进：
- **修复了重大bug**: 原版本只能提取最后一个RCDATA资源，现在能提取所有资源
- **增强编码支持**: 新增UTF-8、UTF-16、ASCII字符串提取
- **数值数据提取**: 能够识别和提取32位整数数据
- **更好的调试信息**: 添加原始数据预览和详细日志
- **去重机制**: 避免重复提取相同位置的数据
- **改进的分析工具**: 提供数据统计和分组显示

## 故障排除

### 常见问题

1. **找不到RC DATA**: 确认exe文件确实包含RC DATA资源
2. **编码错误**: 检查文字是否为BIG5或GBK编码
3. **文字过长**: 修改后的文字不能超过原始长度限制
4. **权限问题**: 确保有写入目标目录的权限

### 调试模式

程序会输出详细的日志信息，帮助诊断问题。
