{"RCDATA_None_0": {"resource_id": null, "language_id": 0, "size": 280, "rva": 916312, "texts": [{"offset": 0, "length": 4, "original_bytes": "54504630", "text": "TPF0", "encoding": "big5"}, {"offset": 5, "length": 13, "original_bytes": "545472616465486973746f7279", "text": "TTradeHistory", "encoding": "big5"}, {"offset": 19, "length": 12, "original_bytes": "5472616465486973746f7279", "text": "TradeHistory", "encoding": "big5"}, {"offset": 32, "length": 4, "original_bytes": "4c656674", "text": "Left", "encoding": "big5"}, {"offset": 40, "length": 3, "original_bytes": "546f70", "text": "Top", "encoding": "big5"}, {"offset": 47, "length": 11, "original_bytes": "426f726465725374796c65", "text": "BorderStyle", "encoding": "big5"}, {"offset": 60, "length": 8, "original_bytes": "62734469616c6f67", "text": "bsDialog", "encoding": "big5"}, {"offset": 69, "length": 7, "original_bytes": "43617074696f6e", "text": "Caption", "encoding": "big5"}, {"offset": 78, "length": 8, "original_bytes": "a5e6b4abbefab57b", "text": "交換歷程", "encoding": "big5"}, {"offset": 79, "length": 3, "original_bytes": "e6b4ab", "text": "洫", "encoding": "utf-8"}, {"offset": 87, "length": 12, "original_bytes": "436c69656e74486569676874", "text": "ClientHeight", "encoding": "big5"}, {"offset": 103, "length": 11, "original_bytes": "436c69656e745769647468", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "encoding": "big5"}, {"offset": 117, "length": 12, "original_bytes": "0a506172656e74466f6e7409", "text": "\nParentFont\t", "encoding": "ascii"}, {"offset": 118, "length": 10, "original_bytes": "506172656e74466f6e74", "text": "ParentFont", "encoding": "big5"}, {"offset": 130, "length": 8, "original_bytes": "506f736974696f6e", "text": "Position", "encoding": "big5"}, {"offset": 140, "length": 14, "original_bytes": "706f53637265656e43656e746572", "text": "poScreenCenter", "encoding": "big5"}, {"offset": 155, "length": 13, "original_bytes": "506978656c73506572496e6368", "text": "PixelsPerInch", "encoding": "big5"}, {"offset": 169, "length": 12, "original_bytes": "600a54657874486569676874", "text": "`\nTextHeight", "encoding": "ascii"}, {"offset": 171, "length": 10, "original_bytes": "54657874486569676874", "text": "TextHeight", "encoding": "big5"}, {"offset": 180, "length": 4, "original_bytes": "74020c00", "text": "787060 (32-bit little endian)", "encoding": "numeric", "value": 787060, "data_type": "uint32_little"}, {"offset": 184, "length": 88, "original_bytes": "08544c697374426f78084c697374426f7831044c656674020003546f70020005576964746803060106486569676874031b0105416c69676e0708616c436c69656e740a4974656d486569676874020c085461624f72646572", "text": "合楌瑳潂ࡸ楌瑳潂ㅸ䰄晥ɴ̀潔ɰԀ楗瑤ͨĆ䠆楥桧ʹě䄅楬湧ࠇ污汃敩瑮䤊整䡭楥桧ɴࠌ慔佢摲牥", "encoding": "utf-16le"}, {"offset": 185, "length": 8, "original_bytes": "544c697374426f78", "text": "TListBox", "encoding": "big5"}, {"offset": 194, "length": 8, "original_bytes": "4c697374426f7831", "text": "ListBox1", "encoding": "big5"}, {"offset": 203, "length": 4, "original_bytes": "4c656674", "text": "Left", "encoding": "big5"}, {"offset": 208, "length": 4, "original_bytes": "0003546f", "text": "218223 (32-bit big endian)", "encoding": "numeric", "value": 218223, "data_type": "uint32_big"}, {"offset": 210, "length": 3, "original_bytes": "546f70", "text": "Top", "encoding": "big5"}, {"offset": 216, "length": 5, "original_bytes": "5769647468", "text": "<PERSON><PERSON><PERSON>", "encoding": "big5"}, {"offset": 225, "length": 6, "original_bytes": "486569676874", "text": "Height", "encoding": "big5"}, {"offset": 235, "length": 5, "original_bytes": "416c69676e", "text": "Align", "encoding": "big5"}, {"offset": 242, "length": 8, "original_bytes": "616c436c69656e74", "text": "alClient", "encoding": "big5"}, {"offset": 251, "length": 10, "original_bytes": "4974656d486569676874", "text": "ItemHeight", "encoding": "big5"}, {"offset": 264, "length": 8, "original_bytes": "5461624f72646572", "text": "TabOrder", "encoding": "big5"}, {"offset": 272, "length": 4, "original_bytes": "02000000", "text": "2 (32-bit little endian)", "encoding": "numeric", "value": 2, "data_type": "uint32_little"}, {"offset": 276, "length": 4, "original_bytes": "00000200", "text": "131072 (32-bit little endian)", "encoding": "numeric", "value": 131072, "data_type": "uint32_little"}], "raw_data_offset": 882520, "raw_data_hex": "545046300d545472616465486973746f72790c5472616465486973746f7279044c65667403de0103546f700331010b426f726465725374796c65070862734469616c6f670743617074696f6e0608a5e6b4abbefab57b0c436c69656e7448656967687403"}}