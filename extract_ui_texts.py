#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI文字提取工具
专门提取和显示用户界面中的Caption、Text、Font.Name等可翻译文字
"""

import json
import sys
import re
from collections import defaultdict

def extract_ui_texts(json_path, output_path=None):
    """提取UI文字"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        ui_texts = []
        
        for resource_key, resource_info in data.items():
            for text_info in resource_info['texts']:
                ui_text = analyze_ui_text(text_info, resource_key)
                if ui_text:
                    ui_texts.append(ui_text)
        
        # 按类型分组
        grouped_texts = group_ui_texts(ui_texts)
        
        # 显示结果
        display_ui_texts(grouped_texts)
        
        # 保存到文件
        if output_path:
            save_ui_texts(grouped_texts, output_path)
            print(f"\nUI文字已保存到: {output_path}")
        
    except Exception as e:
        print(f"提取失败: {e}")

def analyze_ui_text(text_info, resource_key):
    """分析是否为UI文字"""
    text = text_info['text']
    encoding = text_info['encoding']
    
    # 跳过纯英文的属性名
    if is_property_name(text):
        return None
    
    # 跳过控件类型名
    if is_control_type(text):
        return None
    
    # 跳过事件处理函数名
    if is_event_handler(text):
        return None
    
    # 识别UI文字类型
    ui_type = identify_ui_type(text, encoding)
    if ui_type:
        return {
            'text': text,
            'encoding': encoding,
            'type': ui_type,
            'resource': resource_key,
            'offset': text_info['offset'],
            'original_bytes': text_info['original_bytes'],
            'translatable': is_translatable(text, encoding)
        }
    
    return None

def is_property_name(text):
    """判断是否为属性名"""
    property_names = [
        'Caption', 'Text', 'Hint', 'Name', 'Left', 'Top', 'Width', 'Height',
        'Font.Name', 'Font.Charset', 'Font.Color', 'Font.Height', 'Font.Style',
        'BorderStyle', 'Position', 'Align', 'Visible', 'Enabled', 'ReadOnly',
        'TabOrder', 'TabIndex', 'TabStop', 'AutoSize', 'WordWrap', 'MultiLine',
        'Items.Strings', 'Lines.Text', 'Tabs.Strings', 'Filter', 'DefaultExt',
        'OnClick', 'OnShow', 'OnClose', 'OnCreate', 'OnDestroy', 'OnChange',
        'OnEnter', 'OnExit', 'OnKeyPress', 'OnKeyDown', 'ScrollBars'
    ]
    return text in property_names

def is_control_type(text):
    """判断是否为控件类型"""
    return (text.startswith('T') and len(text) > 1 and 
            text[1].isupper() and text.isalpha())

def is_event_handler(text):
    """判断是否为事件处理函数"""
    return (text.endswith('Click') or text.endswith('Show') or 
            text.endswith('Close') or text.endswith('Create') or
            text.endswith('Change') or text.endswith('Enter') or
            text.endswith('Exit') or text.endswith('KeyPress'))

def identify_ui_type(text, encoding):
    """识别UI文字类型"""
    # 中文文字（可能需要翻译）
    if encoding in ['big5', 'gbk'] and any(ord(c) > 127 for c in text):
        if len(text) <= 30:
            return 'caption_text'  # 标题/按钮文字
        else:
            return 'long_text'     # 长文本
    
    # 日文文字
    if encoding == 'shift_jis' and any(ord(c) > 127 for c in text):
        return 'japanese_text'
    
    # UTF-8编码的多语言文字
    if encoding == 'utf-8' and any(ord(c) > 127 for c in text):
        return 'unicode_text'
    
    # 字体名称
    if any(font_keyword in text for font_keyword in ['明體', '宋體', '黑體', 'Arial', 'Times']):
        return 'font_name'
    
    # 文件过滤器
    if '|' in text and ('*.' in text or 'csv' in text.lower()):
        return 'file_filter'
    
    # 英文UI文字（可能需要翻译）
    if encoding == 'ascii' and len(text) > 1 and not text.isupper():
        if any(c.isalpha() for c in text):
            return 'english_text'
    
    return None

def is_translatable(text, encoding):
    """判断是否需要翻译"""
    # 中文、日文、韩文等需要翻译
    if encoding in ['big5', 'gbk', 'shift_jis']:
        return True
    
    # UTF-8中的非ASCII字符可能需要翻译
    if encoding == 'utf-8' and any(ord(c) > 127 for c in text):
        return True
    
    # 英文UI文字可能需要翻译
    if encoding == 'ascii' and len(text) > 2:
        # 排除纯数字、符号等
        if any(c.isalpha() for c in text) and not text.isupper():
            return True
    
    return False

def group_ui_texts(ui_texts):
    """按类型分组UI文字"""
    groups = defaultdict(list)
    
    for ui_text in ui_texts:
        groups[ui_text['type']].append(ui_text)
    
    return dict(groups)

def display_ui_texts(grouped_texts):
    """显示UI文字"""
    print("UI文字提取报告")
    print("=" * 60)
    
    total_texts = sum(len(texts) for texts in grouped_texts.values())
    translatable_count = sum(1 for texts in grouped_texts.values() 
                           for text in texts if text['translatable'])
    
    print(f"总UI文字数: {total_texts}")
    print(f"可翻译文字数: {translatable_count}")
    print()
    
    # 按重要性排序显示
    type_order = [
        'caption_text', 'english_text', 'long_text', 
        'unicode_text', 'japanese_text', 'font_name', 'file_filter'
    ]
    
    for ui_type in type_order:
        if ui_type in grouped_texts:
            texts = grouped_texts[ui_type]
            translatable = [t for t in texts if t['translatable']]
            
            type_names = {
                'caption_text': '标题/按钮文字',
                'english_text': '英文UI文字',
                'long_text': '长文本内容',
                'unicode_text': 'Unicode文字',
                'japanese_text': '日文文字',
                'font_name': '字体名称',
                'file_filter': '文件过滤器'
            }
            
            print(f"{type_names.get(ui_type, ui_type)}: {len(texts)} 个 (可翻译: {len(translatable)})")
            
            # 显示示例
            unique_texts = list(set(t['text'] for t in translatable))
            for i, text in enumerate(unique_texts[:10]):
                display_text = text[:50]
                if len(text) > 50:
                    display_text += "..."
                print(f"  {i+1}. {display_text}")
            
            if len(unique_texts) > 10:
                print(f"  ... 还有 {len(unique_texts) - 10} 个不同的文字")
            print()

def save_ui_texts(grouped_texts, output_path):
    """保存UI文字到文件"""
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# UI文字提取结果\n\n")
        
        for ui_type, texts in grouped_texts.items():
            translatable = [t for t in texts if t['translatable']]
            if not translatable:
                continue
            
            type_names = {
                'caption_text': '标题/按钮文字',
                'english_text': '英文UI文字',
                'long_text': '长文本内容',
                'unicode_text': 'Unicode文字',
                'japanese_text': '日文文字',
                'font_name': '字体名称',
                'file_filter': '文件过滤器'
            }
            
            f.write(f"## {type_names.get(ui_type, ui_type)}\n\n")
            
            # 去重并排序
            unique_texts = {}
            for text_info in translatable:
                text = text_info['text']
                if text not in unique_texts:
                    unique_texts[text] = text_info
            
            for text, info in sorted(unique_texts.items()):
                f.write(f"- **{text}** ({info['encoding']})\n")
                f.write(f"  - 资源: {info['resource']}\n")
                f.write(f"  - 偏移: {info['offset']}\n")
                f.write(f"  - 原始字节: {info['original_bytes']}\n\n")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python extract_ui_texts.py <json文件路径> [输出文件路径]")
        print("示例: python extract_ui_texts.py test_vcl_enhanced.json ui_texts.md")
        return
    
    json_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    extract_ui_texts(json_path, output_path)

if __name__ == '__main__':
    main()
