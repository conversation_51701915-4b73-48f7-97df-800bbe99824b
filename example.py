#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RC DATA 编辑器使用示例
"""

import os
from rc_data_editor import RCDataEditor
import json

def example_extract_and_modify():
    """示例：提取RC DATA并修改"""
    
    # 1. 设置文件路径
    exe_path = input("请输入exe文件路径: ").strip()
    if not os.path.exists(exe_path):
        print("文件不存在！")
        return
    
    json_path = exe_path + '_rc_data.json'
    output_exe = exe_path.replace('.exe', '_modified.exe')
    
    # 2. 创建编辑器并提取数据
    print("正在提取RC DATA...")
    editor = RCDataEditor(exe_path)
    
    if not editor.load_exe():
        print("加载exe文件失败！")
        return
    
    data = editor.extract_rc_data()
    if not data:
        print("未找到RC DATA资源！")
        return
    
    # 3. 保存到JSON
    if editor.save_to_json(json_path):
        print(f"数据已保存到: {json_path}")
        print(f"找到 {len(data)} 个资源")
        
        # 显示找到的文字（改进版本）
        total_texts = 0
        for resource_key, resource_info in data.items():
            text_count = len(resource_info['texts'])
            total_texts += text_count
            print(f"\n资源: {resource_key}")
            print(f"  大小: {resource_info['size']} 字节")
            print(f"  文字项: {text_count}")

            # 按编码类型分组显示
            encoding_groups = {}
            for text_info in resource_info['texts']:
                encoding = text_info['encoding']
                if encoding not in encoding_groups:
                    encoding_groups[encoding] = []
                encoding_groups[encoding].append(text_info)

            for encoding, texts in encoding_groups.items():
                print(f"    [{encoding}] {len(texts)} 项:")
                for i, text_info in enumerate(texts[:3]):  # 只显示前3个
                    text = text_info['text'][:50]
                    if len(text_info['text']) > 50:
                        text += "..."
                    print(f"      {i+1}. {text}")
                if len(texts) > 3:
                    print(f"      ... 还有 {len(texts) - 3} 项")

        print(f"\n总计: {len(data)} 个资源, {total_texts} 个文字项")
    
    # 4. 询问是否要修改
    modify = input("\n是否要修改文字? (y/n): ").lower()
    if modify == 'y':
        print(f"请编辑 {json_path} 文件，修改其中的 'text' 字段")
        print("修改完成后按回车继续...")
        input()
        
        # 5. 写回修改
        print("正在写回修改...")
        if editor.load_from_json(json_path):
            if editor.write_back_to_exe(output_exe):
                print(f"修改后的文件已保存到: {output_exe}")
            else:
                print("写回失败！")
        else:
            print("加载修改后的JSON失败！")

def view_json_structure():
    """查看JSON文件结构"""
    json_path = input("请输入JSON文件路径: ").strip()
    
    if not os.path.exists(json_path):
        print("文件不存在！")
        return
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("JSON文件结构:")
        for resource_key, resource_info in data.items():
            print(f"\n资源ID: {resource_key}")
            print(f"  资源ID: {resource_info['resource_id']}")
            print(f"  语言ID: {resource_info['language_id']}")
            print(f"  大小: {resource_info['size']} 字节")
            print(f"  文字数量: {len(resource_info['texts'])}")
            
            for i, text_info in enumerate(resource_info['texts']):
                print(f"    文字 {i+1}:")
                print(f"      偏移: {text_info['offset']}")
                print(f"      长度: {text_info['length']}")
                print(f"      编码: {text_info['encoding']}")
                print(f"      内容: {text_info['text']}")
    
    except Exception as e:
        print(f"读取JSON文件失败: {e}")

def main():
    """主菜单"""
    while True:
        print("\n=== RC DATA 编辑器 ===")
        print("1. 提取并修改RC DATA")
        print("2. 查看JSON文件结构") 
        print("3. 退出")
        
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == '1':
            example_extract_and_modify()
        elif choice == '2':
            view_json_structure()
        elif choice == '3':
            print("再见！")
            break
        else:
            print("无效选择，请重试")

if __name__ == '__main__':
    main()
