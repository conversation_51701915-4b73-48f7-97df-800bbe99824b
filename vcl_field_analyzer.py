#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VCL字段分析工具
专门分析和显示VCL窗体中的Caption、Text、Font.Name等字段
"""

import json
import sys
from collections import defaultdict

def analyze_vcl_fields(json_path):
    """分析VCL字段"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("VCL字段分析报告")
        print("=" * 60)
        
        # 统计各种字段
        field_stats = defaultdict(list)
        encoding_stats = defaultdict(int)
        total_vcl_fields = 0
        
        for resource_key, resource_info in data.items():
            print(f"\n资源: {resource_key}")
            print(f"大小: {resource_info['size']} 字节")
            
            vcl_fields = []
            
            # 查找VCL相关字段
            for text_info in resource_info['texts']:
                text = text_info['text']
                encoding = text_info['encoding']
                
                # 检查是否为VCL字段
                if is_vcl_field(text_info):
                    vcl_fields.append(text_info)
                    field_name = get_field_name(text)
                    if field_name:
                        field_stats[field_name].append({
                            'resource': resource_key,
                            'text': text,
                            'encoding': encoding,
                            'offset': text_info['offset']
                        })
                        encoding_stats[encoding] += 1
                        total_vcl_fields += 1
            
            if vcl_fields:
                print(f"找到 {len(vcl_fields)} 个VCL字段:")
                
                # 按字段类型分组
                grouped_fields = group_by_field_type(vcl_fields)
                
                for field_type, fields in grouped_fields.items():
                    print(f"\n  {field_type}:")
                    for field in fields[:5]:  # 只显示前5个
                        encoding = field['encoding']
                        text = field['text'][:50]
                        if len(field['text']) > 50:
                            text += "..."
                        print(f"    [{encoding}] {text}")
                    if len(fields) > 5:
                        print(f"    ... 还有 {len(fields) - 5} 个")
        
        # 总结报告
        print("\n" + "=" * 60)
        print("总结报告")
        print("=" * 60)
        print(f"总VCL字段数: {total_vcl_fields}")
        
        print(f"\n编码分布:")
        for encoding, count in sorted(encoding_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_vcl_fields) * 100 if total_vcl_fields > 0 else 0
            print(f"  {encoding}: {count} ({percentage:.1f}%)")
        
        print(f"\n字段类型统计:")
        for field_name, instances in sorted(field_stats.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"  {field_name}: {len(instances)} 个实例")
            
            # 显示该字段的一些示例值
            unique_values = set()
            for instance in instances:
                if instance['text'] not in ['Caption', 'Text', 'Font.Name'] and len(instance['text']) > 1:
                    unique_values.add(instance['text'])
            
            if unique_values:
                print(f"    示例值: {', '.join(list(unique_values)[:3])}")
                if len(unique_values) > 3:
                    print(f"    ... 还有 {len(unique_values) - 3} 个不同的值")
        
    except Exception as e:
        print(f"分析失败: {e}")

def is_vcl_field(text_info):
    """判断是否为VCL字段"""
    text = text_info['text']
    
    # VCL字段名
    vcl_field_names = [
        'Caption', 'Text', 'Hint', 'Name',
        'Font.Name', 'Font.Charset', 'Font.Color', 'Font.Height', 'Font.Style',
        'Items.Strings', 'Lines.Text', 'Tabs.Strings',
        'Filter', 'DefaultExt', 'FileName', 'Title',
        'BorderStyle', 'Position', 'WindowState', 'FormStyle',
        'Align', 'Anchors', 'Visible', 'Enabled', 'ReadOnly',
        'TabOrder', 'TabIndex', 'TabStop',
        'OnClick', 'OnShow', 'OnClose', 'OnCreate', 'OnDestroy',
        'OnChange', 'OnEnter', 'OnExit', 'OnKeyPress', 'OnKeyDown',
        'AutoSize', 'WordWrap', 'ScrollBars', 'MultiLine'
    ]
    
    # VCL控件类型
    vcl_controls = [
        'TForm', 'TPanel', 'TLabel', 'TEdit', 'TMemo', 'TButton',
        'TListBox', 'TComboBox', 'TCheckBox', 'TRadioButton',
        'TGroupBox', 'TTabControl', 'TPageControl', 'TTreeView',
        'TListView', 'TMainMenu', 'TPopupMenu', 'TMenuItem',
        'TOpenDialog', 'TSaveDialog', 'TFontDialog', 'TColorDialog'
    ]
    
    # 检查是否为字段名
    if text in vcl_field_names:
        return True
    
    # 检查是否为控件类型
    if text in vcl_controls:
        return True
    
    # 检查是否为字体名称（通常包含中文或特殊字符）
    if 'font' in text_info.get('field_name', '').lower():
        return True
    
    # 检查是否为Caption或Text的值（包含中文、特殊字符等）
    if (text_info['encoding'] in ['big5', 'gbk', 'utf-8', 'shift_jis'] and 
        len(text) > 1 and 
        any(ord(c) > 127 for c in text)):
        return True
    
    return False

def get_field_name(text):
    """获取字段名称"""
    vcl_field_names = [
        'Caption', 'Text', 'Hint', 'Name',
        'Font.Name', 'Font.Charset', 'Font.Color', 'Font.Height', 'Font.Style',
        'Items.Strings', 'Lines.Text', 'Tabs.Strings',
        'Filter', 'DefaultExt', 'FileName', 'Title',
        'BorderStyle', 'Position', 'WindowState', 'FormStyle',
        'Align', 'Anchors', 'Visible', 'Enabled', 'ReadOnly',
        'TabOrder', 'TabIndex', 'TabStop',
        'OnClick', 'OnShow', 'OnClose', 'OnCreate', 'OnDestroy',
        'OnChange', 'OnEnter', 'OnExit', 'OnKeyPress', 'OnKeyDown',
        'AutoSize', 'WordWrap', 'ScrollBars', 'MultiLine'
    ]
    
    if text in vcl_field_names:
        return text
    
    # 根据内容推测字段类型
    if any(ord(c) > 127 for c in text):
        if len(text) <= 20:
            return "Caption/Text值"
        else:
            return "长文本内容"
    
    if text.startswith('T') and text[1:2].isupper():
        return "控件类型"
    
    if text.endswith('Click') or text.startswith('On'):
        return "事件处理"
    
    return "其他"

def group_by_field_type(vcl_fields):
    """按字段类型分组"""
    groups = defaultdict(list)
    
    for field in vcl_fields:
        field_name = get_field_name(field['text'])
        groups[field_name].append(field)
    
    return dict(groups)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        analyze_vcl_fields(sys.argv[1])
    else:
        print("用法: python vcl_field_analyzer.py <json文件路径>")
        print("示例: python vcl_field_analyzer.py test_complete.json")

if __name__ == '__main__':
    main()
