#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析提取结果的脚本
"""

import json
import sys

def analyze_json(json_path):
    """分析JSON文件内容"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"总资源数: {len(data)}")
        print("=" * 50)
        
        total_texts = 0
        for resource_key, resource_info in data.items():
            text_count = len(resource_info['texts'])
            total_texts += text_count
            size = resource_info['size']
            print(f"{resource_key}:")
            print(f"  大小: {size} 字节")
            print(f"  文字项: {text_count}")
            
            # 显示一些示例文字
            if text_count > 0:
                print("  示例文字:")
                for i, text_info in enumerate(resource_info['texts'][:3]):  # 只显示前3个
                    encoding = text_info['encoding']
                    text = text_info['text'][:50]  # 限制显示长度
                    if len(text_info['text']) > 50:
                        text += "..."
                    print(f"    {i+1}. [{encoding}] {text}")
                if text_count > 3:
                    print(f"    ... 还有 {text_count - 3} 个文字项")
            print()
        
        print(f"总计文字项: {total_texts}")
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        analyze_json(sys.argv[1])
    else:
        print("用法: python analyze_results.py <json文件路径>")
