#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RC DATA BIG5 文字提取和修改工具
用于从exe文件中提取RC DATA中的BIG5文字到JSON，并支持修改后写回
"""

import os
import sys
import json
import struct
import logging
from typing import Dict, List, Tuple, Optional
import pefile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RCDataEditor:
    def __init__(self, exe_path: str):
        """
        初始化RC DATA编辑器
        
        Args:
            exe_path: exe文件路径
        """
        self.exe_path = exe_path
        self.pe = None
        self.rc_data = {}
        self.original_data = {}
        
    def load_exe(self) -> bool:
        """
        加载exe文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            self.pe = pefile.PE(self.exe_path)
            logger.info(f"成功加载 {self.exe_path}")
            return True
        except Exception as e:
            logger.error(f"加载exe文件失败: {e}")
            return False
    
    def extract_rc_data(self) -> Dict:
        """
        提取RC DATA中的资源
        
        Returns:
            Dict: 提取的资源数据
        """
        if not self.pe:
            logger.error("PE文件未加载")
            return {}
        
        extracted_data = {}
        
        try:
            if hasattr(self.pe, 'DIRECTORY_ENTRY_RESOURCE'):
                for resource_type in self.pe.DIRECTORY_ENTRY_RESOURCE.entries:
                    # 查找RCDATA资源类型 (RT_RCDATA = 10)
                    if resource_type.id == pefile.RESOURCE_TYPE['RT_RCDATA']:
                        logger.info("找到 RCDATA 资源")
                        
                        for resource_id in resource_type.directory.entries:
                            for resource_lang in resource_id.directory.entries:
                                # 获取资源数据
                                data_rva = resource_lang.data.struct.OffsetToData
                                size = resource_lang.data.struct.Size
                                data = self.pe.get_data(data_rva, size)
                                
                                # 尝试解析BIG5文字
                                text_data = self.extract_big5_text(data)
                                
                                if text_data:
                                    resource_key = f"RCDATA_{resource_id.id}_{resource_lang.id}"
                                    extracted_data[resource_key] = {
                                        'resource_id': resource_id.id,
                                        'language_id': resource_lang.id,
                                        'size': size,
                                        'rva': data_rva,
                                        'texts': text_data,
                                        'raw_data_offset': self.pe.get_offset_from_rva(data_rva)
                                    }
                                    
                                    # 保存原始数据用于写回
                                    self.original_data[resource_key] = data
                        
        except Exception as e:
            logger.error(f"提取RC DATA失败: {e}")
        
        self.rc_data = extracted_data
        return extracted_data
    
    def extract_big5_text(self, data: bytes) -> List[Dict]:
        """
        从二进制数据中提取BIG5编码的文字
        
        Args:
            data: 二进制数据
            
        Returns:
            List[Dict]: 提取的文字信息列表
        """
        texts = []
        i = 0
        
        while i < len(data):
            # 寻找可能的BIG5字符串
            start_pos = i
            text_bytes = bytearray()
            
            # 检查是否为BIG5字符或ASCII字符
            while i < len(data):
                byte = data[i]
                
                if byte == 0:  # 字符串结束符
                    break
                elif 0x20 <= byte <= 0x7E:  # ASCII可见字符
                    text_bytes.append(byte)
                    i += 1
                elif 0xA1 <= byte <= 0xFE and i + 1 < len(data):  # BIG5首字节
                    next_byte = data[i + 1]
                    if 0x40 <= next_byte <= 0xFE and next_byte != 0x7F:  # BIG5次字节
                        text_bytes.append(byte)
                        text_bytes.append(next_byte)
                        i += 2
                    else:
                        break
                else:
                    break
            
            # 如果找到了有意义的文字(长度大于1)
            if len(text_bytes) > 1:
                try:
                    # 尝试用BIG5解码
                    decoded_text = text_bytes.decode('big5', errors='ignore')
                    if decoded_text.strip():  # 如果解码后有内容
                        texts.append({
                            'offset': start_pos,
                            'length': len(text_bytes),
                            'original_bytes': text_bytes.hex(),
                            'text': decoded_text,
                            'encoding': 'big5'
                        })
                except UnicodeDecodeError:
                    # 如果BIG5解码失败，尝试其他编码
                    try:
                        decoded_text = text_bytes.decode('gbk', errors='ignore')
                        if decoded_text.strip():
                            texts.append({
                                'offset': start_pos,
                                'length': len(text_bytes),
                                'original_bytes': text_bytes.hex(),
                                'text': decoded_text,
                                'encoding': 'gbk'
                            })
                    except:
                        pass
            
            i = max(i, start_pos + 1)  # 防止无限循环
        
        return texts
    
    def save_to_json(self, json_path: str) -> bool:
        """
        将提取的数据保存到JSON文件
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.rc_data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到 {json_path}")
            return True
        except Exception as e:
            logger.error(f"保存JSON文件失败: {e}")
            return False
    
    def load_from_json(self, json_path: str) -> bool:
        """
        从JSON文件加载修改后的数据
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                self.rc_data = json.load(f)
            logger.info(f"从 {json_path} 加载数据成功")
            return True
        except Exception as e:
            logger.error(f"加载JSON文件失败: {e}")
            return False
    
    def write_back_to_exe(self, output_path: str) -> bool:
        """
        将修改后的数据写回exe文件
        
        Args:
            output_path: 输出exe文件路径
            
        Returns:
            bool: 写入是否成功
        """
        if not self.pe or not self.rc_data:
            logger.error("PE文件或数据未准备好")
            return False
        
        try:
            # 复制原文件
            with open(self.exe_path, 'rb') as src:
                with open(output_path, 'wb') as dst:
                    dst.write(src.read())
            
            # 修改复制的文件
            with open(output_path, 'r+b') as f:
                for resource_key, resource_info in self.rc_data.items():
                    if resource_key in self.original_data:
                        # 重建二进制数据
                        new_data = self.rebuild_binary_data(
                            self.original_data[resource_key],
                            resource_info['texts']
                        )
                        
                        # 写入新数据
                        offset = resource_info['raw_data_offset']
                        f.seek(offset)
                        
                        # 确保新数据不超过原始大小
                        if len(new_data) <= resource_info['size']:
                            f.write(new_data)
                            # 如果新数据更短，用0填充剩余空间
                            if len(new_data) < resource_info['size']:
                                f.write(b'\x00' * (resource_info['size'] - len(new_data)))
                        else:
                            logger.warning(f"新数据大小超过原始大小，跳过 {resource_key}")
            
            logger.info(f"修改后的文件已保存到 {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"写回exe文件失败: {e}")
            return False
    
    def rebuild_binary_data(self, original_data: bytes, texts: List[Dict]) -> bytes:
        """
        根据修改后的文字重建二进制数据
        
        Args:
            original_data: 原始二进制数据
            texts: 修改后的文字列表
            
        Returns:
            bytes: 重建的二进制数据
        """
        new_data = bytearray(original_data)
        
        # 按偏移量倒序排序，避免修改时位置偏移问题
        sorted_texts = sorted(texts, key=lambda x: x['offset'], reverse=True)
        
        for text_info in sorted_texts:
            offset = text_info['offset']
            length = text_info['length']
            new_text = text_info['text']
            encoding = text_info.get('encoding', 'big5')
            
            try:
                # 将新文字编码为字节
                new_bytes = new_text.encode(encoding)
                
                # 替换原始数据中的对应部分
                if len(new_bytes) <= length:
                    # 新数据不超过原长度
                    new_data[offset:offset + len(new_bytes)] = new_bytes
                    # 如果更短，用0填充
                    if len(new_bytes) < length:
                        new_data[offset + len(new_bytes):offset + length] = b'\x00' * (length - len(new_bytes))
                else:
                    logger.warning(f"新文字 '{new_text}' 编码后长度超过原始长度，将被截断")
                    new_data[offset:offset + length] = new_bytes[:length]
                    
            except UnicodeEncodeError as e:
                logger.error(f"编码文字 '{new_text}' 失败: {e}")
        
        return bytes(new_data)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  提取: python rc_data_editor.py extract <exe文件路径> [输出json路径]")
        print("  写回: python rc_data_editor.py writeback <exe文件路径> <json文件路径> [输出exe路径]")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'extract':
        if len(sys.argv) < 3:
            print("请提供exe文件路径")
            return
        
        exe_path = sys.argv[2]
        json_path = sys.argv[3] if len(sys.argv) > 3 else exe_path + '_rc_data.json'
        
        editor = RCDataEditor(exe_path)
        if editor.load_exe():
            data = editor.extract_rc_data()
            if data:
                editor.save_to_json(json_path)
                print(f"成功提取 {len(data)} 个资源到 {json_path}")
            else:
                print("未找到RC DATA资源")
    
    elif command == 'writeback':
        if len(sys.argv) < 4:
            print("请提供exe文件路径和json文件路径")
            return
        
        exe_path = sys.argv[2]
        json_path = sys.argv[3]
        output_path = sys.argv[4] if len(sys.argv) > 4 else exe_path.replace('.exe', '_modified.exe')
        
        editor = RCDataEditor(exe_path)
        if editor.load_exe():
            editor.extract_rc_data()  # 先提取原始数据
            if editor.load_from_json(json_path):
                if editor.write_back_to_exe(output_path):
                    print(f"成功写回修改到 {output_path}")
                else:
                    print("写回失败")
    
    else:
        print("未知命令，请使用 'extract' 或 'writeback'")

if __name__ == '__main__':
    main()
